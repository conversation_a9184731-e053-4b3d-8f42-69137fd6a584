package com.kerryprops.kip.bill.service.impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Lists;
import com.kerryprops.kip.bill.common.aop.RedisLock;
import com.kerryprops.kip.bill.common.enums.BillPaymentStatus;
import com.kerryprops.kip.bill.common.enums.BillPushStatus;
import com.kerryprops.kip.bill.common.enums.BillStatus;
import com.kerryprops.kip.bill.common.enums.RespCodeEnum;
import com.kerryprops.kip.bill.common.utils.DateUtils;
import com.kerryprops.kip.bill.common.utils.DiffFieldUtils;
import com.kerryprops.kip.bill.common.vo.RespWrapVo;
import com.kerryprops.kip.bill.config.DataMigrationConfig;
import com.kerryprops.kip.bill.dao.entity.AptBill;
import com.kerryprops.kip.bill.dao.entity.AptBillOperator;
import com.kerryprops.kip.bill.feign.clients.BUserClient;
import com.kerryprops.kip.bill.feign.clients.HiveAsClient;
import com.kerryprops.kip.bill.feign.clients.MessageCenterClient;
import com.kerryprops.kip.bill.feign.clients.MessageClient;
import com.kerryprops.kip.bill.feign.entity.MessageDto;
import com.kerryprops.kip.bill.feign.entity.MessageType;
import com.kerryprops.kip.bill.feign.entity.TemplateMsgType;
import com.kerryprops.kip.bill.feign.entity.TenantStaffResponse;
import com.kerryprops.kip.bill.feign.entity.WxTemplateMsgRequestCommand;
import com.kerryprops.kip.bill.service.AptBillOperationService;
import com.kerryprops.kip.bill.service.AptBillPushService;
import com.kerryprops.kip.bill.webservice.vo.resp.OperationChangedFiledRespVo;
import com.kerryprops.kip.hiveas.webservice.resource.resp.RoomResp;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

import static com.kerryprops.kip.bill.common.constants.AppConstants.APT_BILL_PUSH_REDIS_KEY_PREFIX;
import static com.kerryprops.kip.bill.common.utils.BillingFun.exceptionToNull;
import static com.kerryprops.kip.bill.log4j.BSConversationFilter.getCorrelationId;
import static com.kerryprops.kip.bill.log4j.BSConversationFilter.refreshCorrelationId;

@Slf4j
@Service
@AllArgsConstructor
public class AptBillPushServiceImpl implements AptBillPushService {

    private static final int MAX_MSG_LEN = 250;

    private static final String PUSH_BILL_FAIL_TIPS = "请联系系统管理员";

    private static final String INTERNAL_MSG_ERROR_TIPS_PREFIX = "站内信推送异常:";

    private static final String WX_TEMPLATE_ERROR_TIPS_PREFIX = "微信模板推送异常:";

    private final ObjectMapper objectMapper;

    private final AptBillService aptBillService;

    private final HiveAsClient hiveAsClient;

    private final BUserClient bUserClient;

    private final MessageClient messageClient;

    private final MessageCenterClient messageCenterClient;

    private final DataMigrationConfig dm;

    private final AptBillOperationService operationService;

    @Override
    @RedisLock(key = APT_BILL_PUSH_REDIS_KEY_PREFIX, expire = 60L, suffixArgIndexArray = {3, 2})
    public void pushAptBill(Map<String, List<AptBill>> billMap, Set<String> roomIds
            , AptBillOperator aptBillOperator, String projectId) {

        int errorCount = 0;
        int errorCountUnAuth = 0;
        for (String roomId : roomIds) {
            try {
                refreshCorrelationId();
                // push bills for current room
                BillPushStatus billPushStatus = pushOneRoomBill(roomId, billMap.get(roomId), aptBillOperator);
                // check push current room result
                if (BillPushStatus.PUSH_FAILED.equals(billPushStatus)
                        || BillPushStatus.PUSH_FAILED_NO_AUTH.equals(billPushStatus)) {
                    if (BillPushStatus.PUSH_FAILED_NO_AUTH.equals(billPushStatus)) {
                        errorCountUnAuth = errorCountUnAuth + 1;
                    }
                    log.warn("push_room_bill_failed, room: {}, billPushStatus: {}", roomId, billPushStatus);
                    errorCount++;
                } else {
                    log.info("push_room_bill_success, room: {}", roomId);
                }
            } catch (Exception e) {
                log.error("push_room_bill_exception, room: {}", roomId, e);
                errorCount++;
            }
        }
        log.info("push_room_bill_result, total rooms: {}; {} failed, include {} no_auth"
                , roomIds.size(), errorCount, errorCountUnAuth);
    }

    // when fix kip4318, the following function changes return type as BillPushStatus
    // 1. fail to push a room without authorization, display "未绑定授权用户" message
    // 2. fail to push many rooms, display "已推送" message
    public BillPushStatus pushOneRoomBill(String roomId, List<AptBill> roomBills, AptBillOperator aptBillOperator) {
        BillPushStatus billPushStatus = BillPushStatus.PUSH_FAILED;
        RespWrapVo<RoomResp> roomRespRespWrapVo = hiveAsClient.getRoomById(roomId);
        if (!RespWrapVo.isResponseValidWithData(roomRespRespWrapVo)) {
            log.warn("pushOneRoomBill: no room found: {}", roomId);
            logFailedPush(roomBills, "未找到room，请联系系统管理员", aptBillOperator);
            return billPushStatus;
        }
        RoomResp roomResp = roomRespRespWrapVo.getData();
        if (roomResp.getRoom() == null || roomResp.getProject() == null || roomResp.getBuilding() == null) {
            log.error("pushOneRoomBill: room config invalid: {}", roomId);
            logFailedPush(roomBills, "room参数不合法，请联系系统管理员", aptBillOperator);
            return billPushStatus;
        }

        final int AUTHORIZERD = 1; // 授权用户
        final int OWNER = 2; // 业主
        List<TenantStaffResponse> tenantStaffResponses = new ArrayList<>();
        tenantStaffResponses.addAll(requestTenantResponse(AUTHORIZERD, roomId));
        tenantStaffResponses.addAll(requestTenantResponse(OWNER, roomId));

        if (CollectionUtils.isEmpty(tenantStaffResponses)) {
            log.warn("pushOneRoomBill: No room tenant found. {}", roomId);
            billPushStatus = BillPushStatus.PUSH_FAILED_NO_AUTH;
            logFailedPush(roomBills, "未绑定", aptBillOperator);
            return billPushStatus;
        }

        //update bill status to pushed
        return logPush(roomBills, aptBillOperator, tenantStaffResponses, roomResp);
    }

    private List<TenantStaffResponse> requestTenantResponse(int authorizer, String roomId) {
        RespWrapVo<List<TenantStaffResponse>> tsr = bUserClient.getStaffList(null, authorizer, roomId);
        if (!RespWrapVo.isResponseValidWithData(tsr)) {
            log.warn("no room found: {} for authorizer {}", roomId, authorizer);
            return Collections.emptyList();
        }

        List<TenantStaffResponse> tenantStaffResponses = tsr.getData();
        if (CollectionUtils.isEmpty(tenantStaffResponses)) {
            log.warn("no room found: {} for authorizer {}", roomId, authorizer);
            return Collections.emptyList();
        }
        return tenantStaffResponses;
    }

    private BillPushStatus logPush(List<AptBill> roomBills,
                                   AptBillOperator operator,
                                   List<TenantStaffResponse> tenantStaffResponses,
                                   RoomResp roomResp) {

        //新
        List<String> userIds = tenantStaffResponses.stream().map(TenantStaffResponse::getUserId).collect(Collectors.toList());
        BillPushStatus billPushResult = BillPushStatus.PUSHED;

        //update bill status to pushed
        for (AptBill bill : roomBills) {
            AptBill bakBill = new AptBill();
            BeanUtils.copyProperties(bill, bakBill);

            AptBill aptBillNewest = aptBillService.getBillById(bill.getId());
            if (Objects.nonNull(aptBillNewest) && Objects.nonNull(aptBillNewest.getPaymentStatus())
                    && List.of(BillPaymentStatus.PAID, BillPaymentStatus.DIRECT_DEBIT_PAID)
                    .contains(aptBillNewest.getPaymentStatus())) {
                log.info("log_push, bill_already_paid, billNo=[{}]", aptBillNewest.getBillNo());
                continue;
            }

            String comment = sendNotice(bill, userIds, roomResp);
            BillPushStatus billPushStatus = BillPushStatus.PUSHED;
            BillStatus billStatus = null;
            if (Objects.equals(PUSH_BILL_FAIL_TIPS, comment)) {
                billPushResult = billPushStatus = BillPushStatus.PUSH_FAILED;
                comment = comment + "(errorCode: " + getCorrelationId() + ")";
            } else {
                billStatus = BillStatus.TO_BE_PAID;
                bill.setStatus(billStatus);
            }
            bill.setPushStatus(billPushStatus);
            aptBillService.updateBillStatus4Push(bill.getId(), billPushStatus, billStatus);

            List<OperationChangedFiledRespVo> changedFields = DiffFieldUtils.diffFields(bakBill, bill);
            operationService.saveOperationLog(bill, changedFields, operator
                    , StringUtils.abbreviate(comment, MAX_MSG_LEN));
        }
        return billPushResult;
    }

    private void logFailedPush(List<AptBill> roomBills,
                               String comment,
                               AptBillOperator operator) {
        //update bill status to pushed
        for (AptBill bill : roomBills) {
            AptBill bakBill = new AptBill();
            BeanUtils.copyProperties(bill, bakBill);

            bill.setPushStatus(BillPushStatus.PUSH_FAILED);
            aptBillService.updateBillStatus4Push(bill.getId(), BillPushStatus.PUSH_FAILED, null);
            List<OperationChangedFiledRespVo> changedFields = DiffFieldUtils.diffFields(bakBill, bill);
            operationService.saveOperationLog(bill, changedFields, operator, comment);
        }
    }

    private String sendNotice(AptBill bill, List<String> userIds, RoomResp roomResp) {
        String internalMsgComment = null;
        String wxTemplateComment = null;
        //send message
        //“#地址#&#账单期间#&账单费用 #金额#元，请您尽快支付，谢谢您的配合
        //例：“唐山市路北区大里路368号7号楼二单元1001室，2020年10月~2020年12月账单金额为1491.4元，请您尽快支付，谢谢您的配合”
        try {
            String content = roomResp.getProject().getName() +
                    "-" + roomResp.getBuilding().getName() +
                    "-" + roomResp.getRoom().getRoomNo() +
                    ", " + DateUtils.parseDateToStr("yyyy年MM月", bill.getBeginDate()) +
                    "~" + DateUtils.parseDateToStr("yyyy年MM月", bill.getEndDate()) + bill.getCategory() +
                    "账单金额为" + bill.getAmt() + "元，请您尽快支付，谢谢您的配合！";
            MessageDto messageDto = new MessageDto();
            messageDto.setToType("C");
            messageDto.setContent(content);
            messageDto.setUserIds(userIds);
            messageDto.setMessageType(MessageType.BILL_NOTICE);
            messageDto.setParams(Map.of("billId", bill.getId()));
            log.info("kerry_message_req: {}", exceptionToNull(() -> objectMapper.writeValueAsString(messageDto)));
            RespWrapVo<String> msg = messageCenterClient.sendMessage(messageDto);

            if (Objects.isNull(msg) || !RespCodeEnum.SUCCESS.getCode().equals(msg.getCode())) {
                log.error("Send_internal_message_failed, response:{}", msg);
                String mccRespMsgError = Optional.ofNullable(msg).map(RespWrapVo::getMessage).orElse(StringUtils.EMPTY);
                internalMsgComment = INTERNAL_MSG_ERROR_TIPS_PREFIX + mccRespMsgError;
            }
        } catch (Exception e) {
            log.error("Send_internal_message_failed.", e);
            internalMsgComment = INTERNAL_MSG_ERROR_TIPS_PREFIX + e.getMessage();
        }

        try {
            //send wechat template message;
            WxTemplateMsgRequestCommand command = new WxTemplateMsgRequestCommand();
            command.setProjectId(roomResp.getProject().getId());
            command.setBuildingIds(Lists.newArrayList(roomResp.getBuilding().getId()));
            command.setUserIds(userIds);
            command.setFirst("尊敬的业主/住户："); // X新版本不再顯示
            // 新版：bill.getCategory()
            command.setKeyword1(Optional.ofNullable(bill.getCategory()).orElse("账单已出，请查看详情并前往缴费"));
            command.setKeyword2(bill.getYear() + "年" + bill.getMonth() + "月"); // 新舊相同
            command.setKeyword3(roomResp.getProject().getName() + "-" + roomResp.getBuilding().getName()
                    + "-" + roomResp.getRoom().getRoomNo()); // 新舊相同
            // 新版本不再顯示 remark, 复用字段作为”账单金额“
            command.setRemark(bill.getAmt() + "元");
            command.setTemplateMsgType(TemplateMsgType.HOA_FEES_REMINDER);
            command.setUrl(dm.getWxMsgUrl());
            log.info("command: {}", exceptionToNull(() -> objectMapper.writeValueAsString(command)));
            RespWrapVo<Boolean> wx = messageClient.sendWxTemplateMessage(command);

            if (Objects.isNull(wx) || !RespCodeEnum.SUCCESS.getCode().equals(wx.getCode())) {
                log.error("Send_wx_template_message_failed, response:{}", wx);
                String wxRespErrorMsg = Optional.ofNullable(wx).map(RespWrapVo::getMessage).orElse(StringUtils.EMPTY);
                wxTemplateComment = WX_TEMPLATE_ERROR_TIPS_PREFIX + wxRespErrorMsg;
            }
        } catch (Exception e) {
            log.error("Send_wx_template_message_failed.", e);
            wxTemplateComment = WX_TEMPLATE_ERROR_TIPS_PREFIX + e.getMessage();
        }

        // 站内信/微信模板都推送失败，则认为推送失败，返回的备注为请联系管理员；
        // 站内信/微信模板只有一个推送成功，则认为推送成功，返回的备注为推送失败的异常信息；
        // 站内信/微信模板都推送成功，则认为推送成功，返回的备注为空字符串；
        return StringUtils.isNotEmpty(internalMsgComment) && StringUtils.isNotEmpty(wxTemplateComment) ?
                PUSH_BILL_FAIL_TIPS : internalMsgComment + wxTemplateComment;
    }

}